namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserForgingComponent))]
  [FriendOf(typeof(UserForgingComponent))]
  public static partial class UserForgingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserForgingComponent self)
    {
    }

    // 学习锻造配方
    public static bool LearnForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会锻造配方
    public static bool HasForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 获取已学会的锻造配方数量
    public static int GetLearnedRecipeCount(this UserForgingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }

  [EntitySystemOf(typeof(UserCraftingComponent))]
  [FriendOf(typeof(UserCraftingComponent))]
  public static partial class UserCraftingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserCraftingComponent self)
    {
    }

    // 学习合成配方
    public static bool LearnCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会合成配方
    public static bool HasCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 获取已学会的合成配方数量
    public static int GetLearnedRecipeCount(this UserCraftingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }
}