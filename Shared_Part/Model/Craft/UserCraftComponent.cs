using System.Collections.Generic;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  // 用户锻造组件
  [ComponentOf(typeof(User))]
  public partial class UserForgingComponent : Entity, IAwake, ISerializeToEntity
  {
    // 已学会的锻造配方
    public HashSet<ForgingRecipeIdEnum> learnedRecipes { get; set; } = new HashSet<ForgingRecipeIdEnum>();
    // 锻造等级
    public int forgingLevel { get; set; } = 1;
    // 当前熟练度值
    public int currentExp { get; set; } = 0;
    // 当前等级的最大熟练度值
    public int maxExpForCurrentLevel { get; set; } = 100;
  }

  // 用户合成组件
  [ComponentOf(typeof(User))]
  public partial class UserCraftingComponent : Entity, IAwake, ISerializeToEntity
  {
    // 已学会的合成配方
    public HashSet<CraftingRecipeIdEnum> learnedRecipes { get; set; } = new HashSet<CraftingRecipeIdEnum>();
    // 合成等级
    public int craftingLevel { get; set; } = 1;
    // 当前熟练度值
    public int currentExp { get; set; } = 0;
    // 当前等级的最大熟练度值
    public int maxExpForCurrentLevel { get; set; } = 100;
  }
}