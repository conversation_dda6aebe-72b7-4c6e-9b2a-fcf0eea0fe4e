using MemoryPack;

namespace <PERSON>YouJi
{
  // 熟练度获得通知消息
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerCraftExpGainMsg)]
  public partial class ServerCraftExpGainMsg : MaoYouMessage, ILocationMessage
  {
    public CraftSystemType systemType; // 系统类型：锻造或合成
    public int expGained; // 获得的熟练度
    public int currentExp; // 当前熟练度
    public int maxExp; // 当前等级最大熟练度
    public int currentLevel; // 当前等级
  }

  // 系统升级通知消息
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ServerCraftLevelUpMsg)]
  public partial class ServerCraftLevelUpMsg : MaoYouMessage, ILocationMessage
  {
    public CraftSystemType systemType; // 系统类型：锻造或合成
    public int newLevel; // 新等级
    public int maxExp; // 新等级的最大熟练度
  }

  // 制作系统类型枚举
  public enum CraftSystemType
  {
    Forging = 1, // 锻造
    Crafting = 2 // 合成
  }
}
