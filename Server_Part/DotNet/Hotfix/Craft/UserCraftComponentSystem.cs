namespace MaoYouJi
{

  [EntitySystemOf(typeof(UserCraftingComponent))]
  [FriendOf(typeof(UserCraftingComponent))]
  public static partial class UserCraftingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserCraftingComponent self)
    {
    }

    // 学习合成配方
    public static bool LearnCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会合成配方
    public static bool HasCraftingRecipe(this UserCraftingComponent self, CraftingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 增加合成系统熟练度
    public static void AddCraftingExp(this UserCraftingComponent self, int exp)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      self.currentExp += exp;

      // 发送熟练度获得通知
      user.SendChat($"合成熟练度增加 {exp} 点");

      // 检查是否满足升级条件
      self.CheckCraftingLevelUp();
    }

    // 检查合成系统升级
    public static void CheckCraftingLevelUp(this UserCraftingComponent self)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      while (self.currentExp >= self.maxExpForCurrentLevel)
      {
        self.currentExp -= self.maxExpForCurrentLevel;
        self.craftingLevel++;

        // TODO: 计算下一级所需的最大熟练度
        // 这里暂时使用简单的线性增长，实际应该根据游戏设计调整
        self.maxExpForCurrentLevel = 100 + (self.craftingLevel - 1) * 50;

        // 发送升级通知
        user.SendChat($"合成等级提升到 {self.craftingLevel} 级！");
      }
    }

    // 获取已学会的合成配方数量
    public static int GetLearnedRecipeCount(this UserCraftingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }
}