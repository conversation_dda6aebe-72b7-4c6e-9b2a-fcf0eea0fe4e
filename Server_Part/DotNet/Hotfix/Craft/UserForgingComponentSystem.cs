namespace MaoYouJi
{
  [EntitySystemOf(typeof(UserForgingComponent))]
  [FriendOf(typeof(UserForgingComponent))]
  public static partial class UserForgingComponentSystem
  {
    [EntitySystem]
    private static void Awake(this UserForgingComponent self)
    {
    }

    // 学习锻造配方
    public static bool LearnForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      if (self.learnedRecipes.Contains(recipeId))
      {
        return false; // 已经学会了
      }
      self.learnedRecipes.Add(recipeId);
      return true;
    }

    // 检查是否已学会锻造配方
    public static bool HasForgingRecipe(this UserForgingComponent self, ForgingRecipeIdEnum recipeId)
    {
      return self.learnedRecipes.Contains(recipeId);
    }

    // 增加锻造系统熟练度
    public static void AddForgingExp(this UserForgingComponent self, int exp)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      self.currentExp += exp;

      // 发送熟练度获得通知
      user.SendChat($"锻造熟练度增加 {exp} 点");

      // 检查是否满足升级条件
      self.CheckForgingLevelUp();
    }

    // 检查锻造系统升级
    public static void CheckForgingLevelUp(this UserForgingComponent self)
    {
      User user = self.GetParent<User>();
      if (user == null) return;

      while (self.currentExp >= self.maxExpForCurrentLevel)
      {
        self.currentExp -= self.maxExpForCurrentLevel;
        self.forgingLevel++;

        // TODO: 计算下一级所需的最大熟练度
        // 这里暂时使用简单的线性增长，实际应该根据游戏设计调整
        self.maxExpForCurrentLevel = 100 + (self.forgingLevel - 1) * 50;

        // 发送升级通知
        user.SendChat($"锻造等级提升到 {self.forgingLevel} 级！");
      }
    }

    // 获取已学会的锻造配方数量
    public static int GetLearnedRecipeCount(this UserForgingComponent self)
    {
      return self.learnedRecipes.Count;
    }
  }

}